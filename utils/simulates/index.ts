import { CoinStruct, DryRunTransactionBlockResponse } from "@mysten/sui/client";
import BigNumber from "bignumber.js";
import { EDex } from "@/enums";
import {
  getReferenceGasPrice,
  getSuiClient,
  RETRY_MAX_ATTEMPT,
  RETRY_MAX_TIMEOUT,
  RETRY_MIN_TIMEOUT,
  suiClient,
} from "../suiClient";
import { Transaction } from "@mysten/sui/transactions";
import {
  SUI_DECIMALS,
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
} from "../contants";
import {
  compareBN,
  convertDecToMist,
  dividedBN,
  isZero,
  toStringBN,
  toTokenAmount,
} from "../helper";
import retry from "async-retry";
import { TCoinMetadata, TPosition } from "@/types";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import DexesFactory from "./dexes/DexesFactory";

export interface RouterSwapEvent {
  amount_in: string;
  amount_out: string;
  recipient: string;
}

export type TDexPool = {
  objectId?: string;
  dex: EDex;
  liquidity?: string;
};
export const ROUTER_BUY_EVENT = "BuyEvent";
export const ROUTER_SELL_EVENT = "SellEvent";

export const MIN_SUI_LIQUIDITY_TO_SIMULATE = 100;

export const simulateBuyExactIn = async (
  walletAddress: string,
  buyAmount: BigNumber | string,
  tokenIn: TCoinMetadata,
  tokenOut: TCoinMetadata,
  pool: TDexPool,
  isBuyBySuiToken = false
) => {
  let client = suiClient;
  const exactAmountIn = convertDecToMist(
    buyAmount,
    isBuyBySuiToken ? SUI_DECIMALS : tokenIn.decimals
  );
  try {
    return await retry(
      async () => {
        const gasBasePrice = await getReferenceGasPrice();
        let tx: Transaction | null = null;
        const instance = DexesFactory.getDexesInstance(pool.dex);

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          return await instance.extractBaseTokenOut(
            walletAddress,
            exactAmountIn,
            tokenIn,
            tokenOut,
            pool.objectId,
            gasBasePrice
          );
        }

        if (pool.dex === EDex.SUIAIFUN) {
          tx = await instance.extractBaseTokenOut(
            walletAddress,
            exactAmountIn,
            tokenIn,
            tokenOut,
            pool.objectId,
            gasBasePrice,
            isBuyBySuiToken
          );
        } else {
          tx = await instance.extractBaseTokenOut(
            walletAddress,
            exactAmountIn,
            tokenIn,
            tokenOut,
            pool.objectId,
            gasBasePrice
          );
        }

        if (!tx) {
          throw new Error("Failed to build transaction");
        }

        const simulateResponse = await client.dryRunTransactionBlock({
          transactionBlock: await tx.build({
            client: client,
          }),
        });
        if (!simulateResponse) {
          throw new Error("Simulate response not found");
        }
        let tokenOutAmount = "0";
        if (pool.dex === EDex.SUIAIFUN) {
          tokenOutAmount = await extractTokenOutAmountFromSuiai(
            simulateResponse,
            tokenOut
          );
        } else {
          tokenOutAmount = await extractTokenOutAmount(simulateResponse);
        }
        return toTokenAmount(tokenOutAmount, tokenOut.decimals);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`simulateBuyExactIn ${pool.dex} retry ${attempt}`, e);
          client = getSuiClient(attempt);
        },
      }
    );
  } catch (e) {
    return "0";
  }
};

export const simulateBuyExactInManyWallet = async (
  walletAddresses: string[],
  buyAmount: BigNumber,
  tokenIn: TCoinMetadata,
  tokenOut: TCoinMetadata,
  pool: TDexPool
) => {
  const tokenAmounts = await Promise.all(
    walletAddresses.map(async (walletAddress) => {
      return await simulateBuyExactIn(
        walletAddress,
        buyAmount,
        tokenIn,
        tokenOut,
        pool
      );
    })
  );
  return tokenAmounts.reduce(
    (sum, amount) => new BigNumber(sum).plus(amount).toString(),
    "0"
  );
};

export const simulateSellExactIn = async (
  walletAddress: string,
  sellPercent: number,
  tokenIn: TCoinMetadata,
  tokenOut: TCoinMetadata,
  pool: TDexPool
) => {
  let client = suiClient;
  try {
    return await retry(
      async () => {
        let tx: Transaction | null = null;
        const gasBasePrice = await getReferenceGasPrice();
        const instance = DexesFactory.getDexesInstance(pool.dex);
        tx = await instance.extractQuoteTokenOut(
          walletAddress,
          tokenIn,
          tokenOut,
          sellPercent,
          pool.objectId,
          gasBasePrice
        );
        if (!tx) {
          throw new Error("Failed to build transaction");
        }

        const simulateResponse = await client.dryRunTransactionBlock({
          transactionBlock: await tx.build({
            client: client,
          }),
        });
        if (!simulateResponse) {
          throw new Error("Simulate response not found");
        }
        let tokenOutAmount = "0";
        if (pool.dex === EDex.SUIAIFUN) {
          tokenOutAmount = await extractTokenOutAmountFromSuiai(
            simulateResponse,
            tokenOut
          );
        } else {
          tokenOutAmount = await extractTokenOutAmount(simulateResponse);
        }
        return toTokenAmount(tokenOutAmount, tokenOut.decimals);
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(`simulateSellExactIn ${pool.dex} retry ${attempt}`, e);
          client = getSuiClient(attempt);
        },
      }
    );
  } catch (e) {
    return "0";
  }
};

export const simulateSellPosition = async (
  position: TPosition,
  userAllCoins: (CoinStruct & { owner: string })[],
  gasBasePrice?: bigint,
  quotePriceUsd?: number | BigNumber | string
) => {
  let client = suiClient;
  const suiBalance = userAllCoins.find(
    (coin) =>
      (normalizeStructTag(coin.coinType) ===
        normalizeStructTag(SUI_TOKEN_ADDRESS_SHORT) ||
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(SUI_TOKEN_ADDRESS_FULL)) &&
      normalizeSuiAddress(coin.owner) ===
        normalizeSuiAddress(position.walletName)
  );
  if (!suiBalance) {
    console.log(
      `Wallet ${position.walletName} not found sui balance ignored simulate position ${position.token.symbol} on ${position.dex}`
    );
    return {
      ...position,
      balanceToQuote: !isZero(quotePriceUsd)
        ? dividedBN(position.balanceUsd || 1, quotePriceUsd || 1)
        : "0",
    };
  }
  try {
    return await retry(
      async () => {
        if (
          compareBN(position?.liquidity || 0, MIN_SUI_LIQUIDITY_TO_SIMULATE) < 0
        ) {
          console.log(
            `simulateSellPosition ${position.token.symbol} on ${position.dex} isRug with liquidity ${position.liquidity}`
          );
          return {
            ...position,
            balanceToQuote: "0",
          };
        }
        let tx: Transaction | null = null;
        const instance = DexesFactory.getDexesInstance(position.dex);
        try {
          tx = await instance.extractQuoteTokenOutPositionWithSponsor(
            position,
            userAllCoins,
            gasBasePrice
          );
        } catch (e: any) {
          if (e?.message?.includes("Insufficient balance")) {
            console.log("simulateSellPosition Insufficient balance", e);
            return {
              ...position,
              balanceToQuote: "0",
            };
          }
        }

        if (!tx) {
          throw new Error("Failed to build transaction");
        }

        const simulateResponse = await client.dryRunTransactionBlock({
          transactionBlock: await tx.build({
            client: client,
          }),
        });
        if (!simulateResponse) {
          throw new Error("Simulate response not found");
        }
        let tokenOutAmount = "0";
        if (position.dex === EDex.SUIAIFUN) {
          tokenOutAmount = await extractTokenOutAmountFromSuiai(
            simulateResponse,
            position.tokenQuote
          );
        } else {
          tokenOutAmount = await extractTokenOutAmount(simulateResponse);
          if (BigNumber(tokenOutAmount).isZero()) {
            console.log(
              "simulateSellPosition tokenOutAmount is zero",
              simulateResponse,
              position
            );
          }
        }
        const tokenOut = toTokenAmount(
          tokenOutAmount,
          position?.tokenQuote?.decimals || SUI_DECIMALS
        );
        return {
          ...position,
          balanceToQuote: tokenOut,
        };
      },
      {
        retries: RETRY_MAX_ATTEMPT,
        minTimeout: RETRY_MIN_TIMEOUT,
        maxTimeout: RETRY_MAX_TIMEOUT,
        onRetry: (e, attempt) => {
          console.log(
            `simulateSellPosition ${position.token.symbol} on ${position.dex} retry ${attempt}`,
            e
          );
          client = getSuiClient(attempt);
        },
      }
    );
  } catch (e) {
    console.log(
      `simulateSellPosition ${position.token.symbol} on ${position.dex} error`,
      e
    );
    return {
      ...position,
      balanceToQuote: "0",
    };
  }
};

export const extractTokenOutAmount = async (
  simulateResponse: DryRunTransactionBlockResponse
) => {
  if (simulateResponse.effects.status.status === "failure") {
    console.error("Transaction simulation failed: ", simulateResponse);
    return "0";
    // throw new Error(
    //   'Transaction simulation failed: ' + simulateResponse.effects.status.error,
    // );
  }

  const swapEvent = simulateResponse.events.find(
    (event) =>
      event.type.includes(ROUTER_BUY_EVENT) ||
      event.type.includes(ROUTER_SELL_EVENT)
  );

  if (!swapEvent) {
    throw new Error("Swap event not found");
  }

  const swapData = swapEvent.parsedJson as RouterSwapEvent;
  const [amountIn, amountOut] = [swapData.amount_in, swapData.amount_out];

  if (!amountOut || !amountIn) {
    throw new Error("Amount out or amount in is not found");
  }

  const slippage = 0;

  const amountExpect = BigNumber(amountOut).multipliedBy(
    BigNumber(1.0).minus(slippage)
  );

  return toStringBN(amountExpect);
};

export const extractTokenOutAmountFromSuiai = async (
  simulateResponse: DryRunTransactionBlockResponse,
  tokenOut: TCoinMetadata
) => {
  if (simulateResponse.effects.status.status === "failure") {
    console.error("Transaction simulation failed: ", simulateResponse);
    return "0";
    // throw new Error(
    //   'Transaction simulation failed: ' + simulateResponse.effects.status.error,
    // );
  }
  const balanceChanges = simulateResponse.balanceChanges;
  const balanceChange = balanceChanges.find(
    (change) =>
      normalizeStructTag(change.coinType) ===
      normalizeStructTag(tokenOut.address)
  );
  if (!balanceChange) {
    return "0";
  }

  return toStringBN(balanceChange?.amount || 0);
};
