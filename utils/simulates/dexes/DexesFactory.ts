import { capitalize } from "lodash";
import BluefinSimulate from "../dexes/BluefinSimulate";
import BluemoveSimulate from "../dexes/BluemoveSimulate";
import CetusSimulate from "../dexes/CetusSimulate";
import FlowxSimulate from "../dexes/FlowxSimulate";
import MovepumpSimulate from "../dexes/MovepumpSimulate";
import MoonbagsSimulate from "../dexes/MoonbagsSimulate";
import SevenkfunSimulate from "../dexes/SevenkfunSimulate";
import SuiaiSimulate from "../dexes/SuiaiSimulate";
import TurbospumpSimulate from "./TurbospumpSimulate";
import TurbosSimulate from "./TurbosSimulate";
import { EDex } from "@/enums";

const dexesMap: Record<string, { new (): any }> = {
  BluefinSimulate,
  BluemoveSimulate,
  CetusSimulate,
  FlowxSimulate,
  MovepumpSimulate,
  SevenkfunSimulate,
  SuiaiSimulate,
  TurbospumpSimulate,
  TurbosSimulate,
  MoonbagsSimulate,
};

export default class DexesFactory {
  static getClassName = (name: EDex) => {
    if (name === EDex.TURBOSFINANCE) {
      return "turbos";
    }

    if (name === EDex.TURBOSFUN) {
      return "turbospump";
    }

    return name;
  };

  static getDexesInstance(className: EDex) {
    const pollClassName: any = `${capitalize(
      this.getClassName(className)
    )}Simulate`;
    const ClassConstructor = dexesMap[pollClassName];

    if (!ClassConstructor) {
      console.error(`Class ${className} not found`);
      return null;
    }

    return new ClassConstructor();
  }
}
