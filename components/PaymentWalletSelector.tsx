import * as React from "react";
import { useState } from "react";
import {
  ChevronDownIcon,
  SearchIcon,
  RadioIcon,
  RadioCheckedIcon,
} from "@/assets/icons";
import { TWallet } from "@/types";
import { formatShortAddress } from "@/utils/format";
import { AppNumber } from "@/components/AppNumber";
import { useEffect } from "react";
import { useRef } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

export const PaymentWalletSelector = ({
  setSelectedWalletAddress,
  isOnlySelect = false,
  disabled = false,
}: {
  isOnlySelect?: boolean;
  disabled?: boolean;
  setSelectedWalletAddress: (walletAddress: string) => void;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [search, setSearch] = useState<string>("");
  const [walletsShow, setWalletsShow] = useState<TWallet[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<TWallet | null>(null);

  const contentRef = useRef<any>(null);

  const handleClickOutside = (event: Event) => {
    if (
      contentRef.current &&
      !contentRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  useEffect(() => {
    let dataWallet = wallets;
    if (search) {
      dataWallet = dataWallet.filter(
        (item) =>
          item.address?.toLowerCase().includes(search?.toLowerCase()) ||
          item.aliasName?.toLowerCase().includes(search?.toLowerCase())
      );
    }
    setWalletsShow(dataWallet);
  }, [wallets, search]);

  const _renderButton = () => {
    if (isOnlySelect) {
      return (
        <div
          onClick={() => setShowDropdown(true)}
          className="border-white-100 bg-white-50 flex cursor-pointer items-center justify-between gap-[8px] rounded-[6px] border px-[8px] py-[7px]"
        >
          {selectedWallet ? (
            <div className="flex items-center gap-2">
              <div className="body-sm-medium-12 border-white-100 bg-white-100 max-w-[100px] truncate rounded-[4px] border px-[4px]">
                {selectedWallet?.aliasName}
              </div>
              <div className="text-white-900 text-[12px] font-[400] leading-[18px]">
                {selectedWallet?.address}
              </div>
            </div>
          ) : (
            <div className="body-sm-regular-12 text-white-300">
              Choose your wallet
            </div>
          )}

          <div>
            <ChevronDownIcon className="h-[12px] w-[12px]" />
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="border-white-100 bg-white-50 flex items-center justify-between gap-[8px] rounded-[6px] border px-[8px] py-[7px]">
          {selectedWallet ? (
            <div className="body-sm-medium-12 border-white-100 bg-white-100 max-w-[100px] truncate rounded-[4px] border px-[4px]">
              {selectedWallet?.aliasName}
            </div>
          ) : (
            <div className="flex max-w-[275px] flex-1 flex-wrap gap-2">
              <input
                disabled={disabled}
                placeholder="Enter wallet to transfer"
                onChange={(e) =>
                  setSelectedWalletAddress(e.target.value.trim())
                }
                className="body-sm-regular-12 placeholder:text-white-300 w-full flex-1 truncate bg-transparent outline-none md:w-[275px]"
              />
            </div>
          )}
          <div
            onClick={() => {
              if (disabled) return;
              setShowDropdown(true);
            }}
            className={` action-xs-medium-12 ${
              disabled
                ? "text-white-500 cursor-not-allowed"
                : "text-brand-500  cursor-pointer"
            }`}
          >
            Choose wallet
          </div>
        </div>
      </>
    );
  };

  const handleSelectAddress = (item: TWallet) => {
    setSelectedWalletAddress(item.address);
    setSelectedWallet(item);
    setShowDropdown(false);
  };

  return (
    <div ref={contentRef} className="relative">
      {_renderButton()}

      {showDropdown && (
        <div
          className="absolute left-0 right-0 z-10 mt-[2px] w-full rounded-[8px] p-[4px]"
          style={{
            background:
              "linear-gradient(0deg, rgba(255, 255, 255, 0.10) 0%,rgba(255, 255, 255, 0.10) 100%), #08090C",
          }}
        >
          <div className="bg-black-900 border-neutral-alpha-100 flex items-center gap-1 rounded-[6px] border p-[8px]">
            <SearchIcon className="h-[16px] w-[16px]" />
            <input
              className="body-sm-regular-12 flex-1 truncate bg-transparent leading-none outline-none"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
              }}
              placeholder="Search"
            />
          </div>

          <div className="customer-scroll h-[140px] overflow-auto pb-[4px]">
            {!walletsShow.length ? (
              <div className="body-sm-regular-12 text-white-700 py-3 text-center">
                No data...
              </div>
            ) : (
              walletsShow.map((item, index) => {
                return (
                  <div
                    key={index}
                    className="border-white-50 grid grid-cols-3 border-b border-dashed px-[6px] py-[8px]"
                  >
                    <div
                      className="flex cursor-pointer items-center gap-2"
                      onClick={() => handleSelectAddress(item)}
                    >
                      {selectedWallet?.address === item.address ? (
                        <RadioCheckedIcon />
                      ) : (
                        <RadioIcon />
                      )}
                      <div className="body-sm-medium-12">{item.aliasName}</div>
                    </div>

                    <div className="body-xs-regular-10 text-white-700 text-center">
                      {formatShortAddress(item.address, 5, 3)}
                    </div>
                    <div className="body-sm-regular-12 flex justify-end gap-1">
                      <div>
                        <AppNumber
                          value={item.balance}
                          className="body-sm-regular-12"
                        />
                      </div>
                      <div className="text-white-700">SUI</div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}
    </div>
  );
};
