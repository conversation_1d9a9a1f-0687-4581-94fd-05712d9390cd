"use client";

import * as React from "react";
import { PairTransactions } from "@/components/Pair/PairTransactions";
import { PairTopTraders } from "@/components/Pair/PairTopTraders";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  MinusIcon,
  CherronUpIcon,
  ChevronDownIcon,
  ITransactionIcon,
  ITopTraderIcon,
  IHolderIcon,
  IPositionIcon,
  IMyTradeIcon,
  IOpenOrderIcon,
  SafeIcon,
} from "@/assets/icons";
import { useEffect, useState } from "react";
import { TPair, TPairPrice } from "@/types";
import { isEmpty } from "lodash";
import { TableMyPosition } from "@/components/Table/TableMyPosition";
import { TableMyTrade } from "@/components/Table/TableMyTrade";
import { useMediaQuery } from "react-responsive";
import { AppPopover } from "@/components";
import { PairOpenOrders } from "@/components/Pair/PairOpenOrder";
import { useHeightContent } from "@/hooks/useHeightContent";
import { PairHolders } from "./PairHolder";
import { usePairPrice } from "@/hooks/usePairPrice";
const OPTIONS_TXNS = [
  {
    label: "Compact",
    id: "compact",
  },
  {
    label: "Table",
    id: "table",
  },
];

const TabTxns = ({
  isOpen,
  setIsOpen,
  setActiveTab,
  activeTab,
  typeTxns,
  setTypeTxns,
}: {
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  setActiveTab: (value: string) => void;
  activeTab: string;
  typeTxns: string;
  setTypeTxns: (value: string) => void;
}) => {
  return (
    <div>
      <AppPopover
        isOpen={isOpen}
        onToggle={() => setIsOpen(!isOpen)}
        onClose={() => setIsOpen(false)}
        trigger={
          <div
            onClick={() => {
              if (activeTab == "transactions") return;
              setActiveTab("transactions");
            }}
            className={`hover:text-neutral-alpha-1000 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] leading-[16px] hover:font-semibold ${
              activeTab === "transactions"
                ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b font-semibold"
                : "text-neutral-alpha-800 border-0 font-normal"
            }`}
          >
            <ITransactionIcon />
            Txns
            <ChevronDownIcon className="h-4 w-4" />
          </div>
        }
        content={
          <div className="border-white-50 flex flex-col gap-[4px] rounded-[4px] border bg-[#13141a] p-[8px]">
            {OPTIONS_TXNS.map((item, index) => {
              const isActive = typeTxns === item.id;
              return (
                <div
                  onClick={() => {
                    setTypeTxns(item.id);
                    setIsOpen(false);
                  }}
                  key={index}
                  className={`hover:bg-white-50 hover:border-white-100 body-xs-medium-10 flex min-w-[80px] cursor-pointer items-center justify-between gap-1 rounded-[4px] px-[8px] py-[4px] ${
                    isActive ? "bg-white-100" : ""
                  }`}
                >
                  {item.label} {isActive && <SafeIcon />}
                </div>
              );
            })}
          </div>
        }
      />
    </div>
  );
};

export const PairTableList = ({
  expand,
  hide,
  isHidden,
  sectionExpanded,
  isHomePage,
  pair,
}: {
  isHidden?: boolean;
  isHomePage?: boolean;
  sectionExpanded: null | string;
  expand: (value: string) => void;
  hide: (value: string) => void;
  pair: TPair;
}) => {
  const [activeTab, setActiveTab] = useState<string>("transactions");
  const [isOpenOptionTxns, setIsOpenOptionTxns] = useState<boolean>(false);
  const [typeTxns, setTypeTxns] = useState<string>("compact");
  const { pairPrice } = usePairPrice(pair);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const totalPosition = useSelector(
    (state: RootState) => state.user.positions?.length
  );
  const totalOpenOrder = useSelector(
    (state: RootState) => state.user.openOrders?.length
  );
  const isDesktop = useMediaQuery({ query: "(min-width: 992px)" });

  const { heightContent, setIsExpand } = useHeightContent(isHomePage);
  const [totalHolder, setTotalHolder] = useState(0);

  const TAB = [
    {
      id: "transactions",
      name: isDesktop ? "Transactions" : "Txns",
      icon: ITransactionIcon,
    },
    {
      id: "top-traders",
      name: "Top Traders",
      icon: ITopTraderIcon,
    },
    {
      id: "holders",
      name: `Holders ${totalHolder > 0 ? `(${totalHolder})` : ""}`,
      icon: IHolderIcon,
    },
    !!accessToken &&
      isDesktop && {
        id: "my-positions",
        name: `Positions ${totalPosition ? `(${totalPosition})` : ""}`,
        icon: IPositionIcon,
      },
    !!accessToken && {
      id: "my-trades",
      name: "My Trades",
      icon: IMyTradeIcon,
    },
    !!accessToken &&
      isDesktop && {
        id: "open-orders",
        name: `Open Orders ${totalOpenOrder > 0 ? `(${totalOpenOrder})` : ""}`,
        icon: IOpenOrderIcon,
      },
  ].filter(Boolean);

  useEffect(() => {
    if (isEmpty(pair)) return;
    setTotalHolder(pair?.totalHolders || 0);
  }, [pair?.totalHolders]);

  const isExpand = sectionExpanded === "TABLE";

  useEffect(() => {
    setIsExpand(isExpand);
  }, [isExpand]);

  useEffect(() => {
    if (isDesktop) {
      setTypeTxns("table");
    }
  }, [isDesktop]);

  const renderHeightContent = () => {
    return heightContent;
  };

  const renderContent = () => {
    switch (activeTab) {
      case "transactions": {
        return (
          <PairTransactions
            heightContent={renderHeightContent()}
            type={typeTxns}
            pair={pair}
          />
        );
      }
      case "top-traders": {
        return (
          <PairTopTraders
            pair={pair}
            setActiveTab={setActiveTab}
            heightContent={renderHeightContent()}
          />
        );
      }
      case "holders": {
        return (
          <PairHolders
            pair={pair}
            setTotalHolder={setTotalHolder}
            setActiveTab={setActiveTab}
            heightContent={renderHeightContent()}
          />
        );
      }
      case "my-positions": {
        return (
          <TableMyPosition pair={pair} heightContent={renderHeightContent()} />
        );
      }
      case "my-trades": {
        return (
          <TableMyTrade heightContent={renderHeightContent()} pair={pair} />
        );
      }
      case "open-orders": {
        return (
          <PairOpenOrders pair={pair} heightContent={renderHeightContent()} />
        );
      }
      default: {
        return (
          <PairTransactions
            heightContent={renderHeightContent()}
            type={typeTxns}
            pair={pair}
          />
        );
      }
    }
  };

  return (
    <>
      <div className="border-neutral-alpha-50 flex items-center justify-between border-b">
        <div
          className={`flex gap-1 ${
            isOpenOptionTxns ? "" : "overflow-x-scroll"
          } hide-scroll`}
        >
          <div className="flex items-center">
            {TAB.map((item: any, index) => {
              if (item.id === "transactions" && !isDesktop) {
                return (
                  <TabTxns
                    key={item.id}
                    typeTxns={typeTxns}
                    setTypeTxns={setTypeTxns}
                    isOpen={isOpenOptionTxns}
                    setIsOpen={setIsOpenOptionTxns}
                    setActiveTab={setActiveTab}
                    activeTab={activeTab}
                  />
                );
              }

              return (
                <div
                  onClick={() => setActiveTab(item.id)}
                  key={index}
                  className={`hover:text-neutral-alpha-1000 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] font-semibold leading-[16px] hover:font-semibold ${
                    activeTab === item.id
                      ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b"
                      : "text-neutral-alpha-800 border-0 font-normal"
                  }`}
                >
                  {/* <item.icon
                    className={`${
                      activeTab === item.id
                        ? 'text-white custom-opacity-svg-active'
                        : 'custom-opacity-svg-inactive'
                    }`}
                  /> */}
                  {item.name}
                </div>
              );
            })}
          </div>
        </div>

        {isDesktop && (
          <div className="flex">
            <div className="cursor-pointer p-2">
              {sectionExpanded === "CHART" ? (
                <MinusIcon className="opacity-50" />
              ) : (
                <MinusIcon onClick={() => hide("TABLE")} />
              )}
            </div>
            <div className="cursor-pointer p-2">
              {sectionExpanded === "TABLE" ? (
                <ChevronDownIcon width={16} onClick={() => hide("TABLE")} />
              ) : (
                <CherronUpIcon onClick={() => expand("TABLE")} />
              )}
            </div>
          </div>
        )}
      </div>
      {!isHidden && pair?.slug && renderContent()}
    </>
  );
};
