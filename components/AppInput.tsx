import clsx from "clsx";
import React, {
  DetailedHTMLProps,
  forwardRef,
  InputHTMLAttributes,
  ReactNode,
} from "react";

interface IProps
  extends DetailedHTMLProps<
    InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  > {
  rootClassName?: string;
  className?: string;
  icon?: ReactNode;
  label?: string;
  required?: boolean;
  error?: string;
}

const AppInput = forwardRef(
  (props: IProps, ref: React.Ref<HTMLInputElement>) => {
    const {
      rootClassName,
      className,
      icon,
      label,
      required,
      error,
      ...restProps
    } = props;
    return (
      <div className="app-input">
        {label && (
          <label className="mb-1 block text-sm font-semibold ">
            {label} {required && <span className="text-red-500">*</span>}
          </label>
        )}
        <div
          className={clsx(
            "border-white-100 bg-black-900 flex items-center gap-1 rounded-[6px] border p-2",
            rootClassName
          )}
        >
          {!!icon && icon}
          <input
            ref={ref}
            className={clsx(
              "text-md w-full bg-transparent py-2 leading-4 outline-none",
              className
            )}
            {...restProps}
          />
        </div>
        {error && <div className="text-sm text-red-500">{error}</div>}
      </div>
    );
  }
);

AppInput.displayName = "AppInput";

export default AppInput;
