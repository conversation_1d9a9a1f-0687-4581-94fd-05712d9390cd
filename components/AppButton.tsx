"use client";

import React, { ReactNode } from "react";

export const AppButton = ({
  children,
  onClick,
  size = "medium",
  className = "",
  variant = "primary",
  disabled = false,
}: {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  variant?: "primary" | "secondary" | "buy" | "sell" | "outline" | "boost";
  size?: "small" | "medium" | "large";
}) => {
  const getClassByVariant = (disabled: boolean) => {
    switch (variant) {
      case "primary": {
        if (disabled) {
          return "bg-white-800 text-black-900 opacity-50";
        }
        return "bg-white-1000 hover:bg-white-800 text-black-900";
      }
      case "secondary": {
        if (disabled) {
          return "opacity-50 bg-white-100 hover:bg-white-100";
        }
        return "bg-white-100 hover:bg-white-50 text-white-1000";
      }
      case "sell": {
        if (disabled) {
          return "opacity-70 bg-red-900 border-red-800 border text-red-700";
        }
        return "bg-red-800 hover:bg-red-900 border-red-800 border text-red-500";
      }
      case "buy": {
        if (disabled) {
          return "opacity-70 bg-brand-900 border-brand-800 border text-brand-700";
        }
        return "bg-brand-800 hover:bg-brand-900 border-brand-800 border text-brand-500";
      }
      case "outline": {
        return "hover:bg-white-100 hover:border-white-300 border border-white-150 text-white-1000";
      }
      case "boost": {
        return "bg-orange-800 border border-orange-800 text-orange-500";
      }
      default: {
        return "bg-white-1000 hover:bg-white-800 text-black-900";
      }
    }
  };

  const getClassSize = () => {
    switch (size) {
      case "large": {
        return "px-[16px] py-[10px] action-sm-medium-14 rounded-[8px]";
      }
      case "medium": {
        return "p-[8px] action-xs-medium-12 rounded-[6px]";
      }
      case "small": {
        return "p-[4px] action-xs-medium-12 rounded-[4px]";
      }

      default: {
        return "p-[8px] action-xs-medium-12 rounded-[6px]";
      }
    }
  };

  const handleClick = () => {
    if (disabled) return;
    if (typeof onClick === "function") {
      onClick();
    }
  };

  return (
    <div
      onClick={handleClick}
      className={`${getClassSize()} ${
        disabled ? "cursor-not-allowed" : "cursor-pointer"
      } flex items-center justify-center ${getClassByVariant(
        disabled
      )} ${className}`}
    >
      {children}
    </div>
  );
};
