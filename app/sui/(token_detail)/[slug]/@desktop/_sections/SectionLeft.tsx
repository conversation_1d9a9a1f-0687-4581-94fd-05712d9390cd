"use client";

import React, { useContext, useEffect, useState } from "react";
import {
  ChevronLeftIcon,
  FlashIconBoost,
  StarIcon,
  Telegram,
  TwitterIcon,
} from "@/assets/icons";
import config from "@/config";
import Storage from "@/libs/storage";
import moment from "moment";
import { getTimeFormatBoots, multipliedBN, sleep } from "@/utils/helper";
import { RootPairContext } from "@/app/sui/(token_detail)/provider";
import { TPair, TPairPrice, TPairStats, TPairToken } from "@/types";
import { TokenInfos } from "@/components/Pair/TokenInfos";
import { PairTradingStats } from "@/components/Pair/PairTradingStats";
import { AppAvatarToken, AppButton, AppCopy, AppNumber } from "@/components";
import { PairAuditCheck } from "@/components/Pair/AuditCheck";
import { formatNumber } from "@/utils/format";
import { useFavouritePairs } from "@/hooks/useFavouritePairs";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { getCirculatingSupply, overridePairStatIfNeed } from "@/utils/pair";
import { isEmpty } from "lodash";
import ModalBoost from "@/modals/ModalBoost";
import { usePairBoost } from "@/app/sui/(token_detail)/_hooks/usePairBoost";

type Props = {
  isHideLeftPanelState: boolean;
  setIsHideLeftPanelState: (isHideLeftPanelState: boolean) => void;
};

const TABS_CHANGE = [
  {
    value: "5m",
    name: "5M",
  },
  {
    value: "1h",
    name: "1H",
  },
  {
    value: "6h",
    name: "6H",
  },
  {
    value: "24h",
    name: "24H",
  },
];

const CollapseContent = ({
  isHideLeftPanelState,
  setIsHideLeftPanelState,
}: Props) => {
  const [tokenBase, setTokenBase] = useState<TPairToken | any>({});
  const { pair, pairPrice } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
  };
  const totalSupply = pair?.tokenBase?.totalSupply;
  const { handleFavourite, isFavourite } = useFavouritePairs();
  const [liquidityUsd, setLiquidityUsd] = useState("0");
  const [capUsd, setCapUsd] = useState("0");
  const [stats, setStats] = useState<TPairStats>();
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const handleWhenPairStatsChange = async (event: TBroadcastEvent) => {
    let data: any = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }
    setIsUpdating(true);

    setLiquidityUsd(data?.liquidityUsd);
    setCapUsd(multipliedBN(data?.priceUsd, getCirculatingSupply(pair)));
    data = overridePairStatIfNeed(data);
    setStats(data);
    await sleep(300);
    setIsUpdating(false);
  };

  useEffect(() => {
    if (isEmpty(pair) || !pairPrice) return;
    setLiquidityUsd(pair.liquidityUsd);
    setCapUsd(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
  }, [pair?.pairId, pairPrice]);

  useEffect(() => {
    setTokenBase(pair?.tokenBase);
  }, [pair?.pairId]);
  useEffect(() => {
    if (isEmpty(pair) || !pair?.stats) return;
    const pairStatsRes = overridePairStatIfNeed(pair.stats);
    setStats(pairStatsRes);
  }, [pair?.stats]);

  const handleWhenSocialsChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.tokenAddress !== pair?.tokenBase?.address) {
      return;
    }

    setTokenBase({
      ...tokenBase,
      logoImageUrl: data.logo_image_url,
      bannerImageUrl: data.banner_image_url,
      socials: {
        ...tokenBase.socials,
        websites: data.websites,
        socials: data.socials,
      },
    });
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
      handleWhenSocialsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
        handleWhenSocialsChange
      );
    };
  }, [pair.pairId]);
  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.PAIR_STATS_UPDATED,
      handleWhenPairStatsChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenPairStatsChange
      );
    };
  }, [pair?.slug]);
  const ItemMetadata: React.FC<any> = ({
    title,
    content,
    decimals,
    isUSD,
    isNeedUpdate,
  }) => {
    return (
      <div className="flex flex-col items-start pb-2 pl-2">
        <div className="text-neutral-alpha-500 mb-[2px] text-[10px] uppercase leading-[1.6]">
          {title}
        </div>

        <div
          className={`flex w-max items-center gap-1 font-semibold leading-[20px] transition-all duration-300 ${
            isNeedUpdate && isUpdating
              ? "bg-neutral-alpha-600"
              : "bg-transparent"
          }`}
        >
          <AppNumber
            className="text-[12px] font-semibold leading-[18px]"
            value={content}
            decimals={decimals || 8}
            isForUSD={isUSD}
          />
        </div>
      </div>
    );
  };
  return (
    <div className="transition-all duration-300 ease-linear">
      <div className="mb-[16px]">
        <div className="flex items-center justify-between">
          <div className="mx-[6px] mt-[12px] flex w-full flex-col items-center gap-2">
            <div className="max-tablet:hidden">
              <AppAvatarToken
                size={40}
                className="h-10 w-10"
                image={tokenBase?.logoImageUrl}
              />
            </div>
            <div>
              <div className="mb-[2px] flex items-center gap-1 text-[16px] font-medium leading-[24px]">
                <div className="max-w-[64px] truncate">
                  {pair?.tokenBase?.symbol ? pair?.tokenBase?.name : "Unknown"}
                </div>
                <AppCopy
                  message={pair?.tokenBase?.address}
                  className="h-[14px] w-[14px]"
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <div
            className="bg-neutral-alpha-100 hover:bg-neutral-alpha-50 max-tablet:hidden flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-[4px]"
            onClick={() => handleFavourite(pair.network, pair)}
          >
            <StarIcon
              className={`h-[14px] w-[14px] ${
                isFavourite(pair?.pairId) ? "text-yellow-500" : ""
              }`}
            />
          </div>
        </div>
      </div>
      <div className="mb-4 flex items-center justify-center">
        <div className="flex flex-1 flex-col gap-2">
          <ItemMetadata
            title="FDV"
            content={
              Number(totalSupply || 0) * Number(pairPrice?.priceUsd || 0)
            }
            decimals={pair?.tokenBase?.decimals}
            isUSD
          />
          <ItemMetadata
            title="MC"
            content={capUsd}
            decimals={2}
            isUSD={!!+capUsd}
          />
          <ItemMetadata
            title="LIQ"
            content={liquidityUsd}
            decimals={pair?.tokenBase?.decimals}
            isUSD={!!+multipliedBN(pair?.liquidity, pair?.tokenQuote?.priceUsd)}
          />
          <ItemMetadata
            title="PRICE USD"
            content={pairPrice?.priceUsd}
            decimals={pair?.tokenBase?.decimals}
            isUSD={!!+pairPrice?.priceUsd}
            isNeedUpdate
          />
          <ItemMetadata
            isShowLogo
            title={`PRICE ${pair?.tokenQuote?.symbol || ""}`}
            content={pairPrice?.price}
            decimals={pair?.tokenBase?.decimals}
            isNeedUpdate
          />
        </div>
        <div
          className="border-neutral-alpha-50 bg-neutral-alpha-50 flex h-[104px] w-[14px] cursor-pointer items-center justify-center rounded-[4px] border"
          onClick={() => setIsHideLeftPanelState(!isHideLeftPanelState)}
        >
          <ChevronLeftIcon className="rotate-180 transform" />
        </div>
      </div>
      <div className="border-neutral-alpha-50 flex flex-col border-b">
        {TABS_CHANGE.map((tab, index) => {
          return (
            <div
              key={index}
              className={`hover:bg-white-100 hover:border-white-500 flex cursor-pointer flex-col items-center justify-center p-2 hover:border-b`}
            >
              <div className="text-neutral-alpha-500 text-[12px] font-normal uppercase leading-[1.5]">
                {tab.name}
              </div>

              <div
                className={`${
                  (stats?.percent[tab.value] ?? 0) > 0
                    ? "text-green-500"
                    : "text-red-500"
                } text-[12px] font-semibold leading-[18px]`}
              >
                {stats?.percent[tab.value]
                  ? `${formatNumber(stats?.percent[tab.value], 2)}%`
                  : "-"}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export const SectionLeft = () => {
  const isHideLeftPanel = Storage.getIsHideLeftPanel();
  const [isHideLeftPanelState, setIsHideLeftPanelState] =
    useState(isHideLeftPanel);
  useEffect(() => {
    Storage.setIsHideLeftPanel(isHideLeftPanelState);
  }, [isHideLeftPanelState]);
  const { pair, pairPrice } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
  };
  const { isShowBoostModal, setIsShowBoostModal } = usePairBoost();

  return (
    <>
      <div className="h-full">
        <div
          className={`border-neutral-alpha-50 bg-neutral-alpha-50 relative h-full border-r ${
            isHideLeftPanelState ? "w-[90px]" : "w-[324px]"
          } linear transition-[width_0.5s]`}
        >
          {!isHideLeftPanelState ? (
            <div className={`part-info customer-scroll h-full overflow-auto`}>
              <TokenInfos pair={pair} />
              <PairTradingStats
                pair={pair}
                setIsHideLeftPanelState={setIsHideLeftPanelState}
                isHideLeftPanelState={isHideLeftPanelState}
              />

              <div className="gap-2 p-2">
                <AppButton
                  size="large"
                  variant="boost"
                  onClick={() => setIsShowBoostModal(true)}
                >
                  <div className="flex items-center gap-1">
                    <FlashIconBoost />
                    <span>Boost</span>

                    {pair?.tokenBase?.boostFactor &&
                      moment(
                        pair?.tokenBase?.isBoostedUntil,
                        "YYYY-MM-DD H:mm:ss.S Z"
                      ).valueOf() > moment().valueOf() && (
                        <span className="body-xs-medium-10 inline-flex h-[20px] items-center justify-center rounded border border-orange-900 bg-orange-900 px-1">
                          {getTimeFormatBoots(pair?.tokenBase?.boostFactor)}
                        </span>
                      )}
                  </div>
                </AppButton>
              </div>

              <PairAuditCheck />
              <div
                className="border-neutral-alpha-50 absolute bottom-0 flex w-full items-center justify-between border-t px-[12px] py-[10px]"
                style={{
                  backdropFilter: "blur(7.5px)",
                  background:
                    "linear-gradient(0deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%)",
                }}
              >
                <a
                  href={config.customerSupportUrl}
                  target="_blank"
                  className="text-white-1000 cursor-pointer text-[12px] leading-[18px] hover:underline"
                >
                  Customer Support
                </a>
                <div className="flex gap-4">
                  <a
                    href={config.linkSocial.twitter}
                    target="_blank"
                    className="text-neutral-alpha-800 hover:text-neutral-alpha-1000 "
                  >
                    <TwitterIcon />
                  </a>
                  <a
                    href={config.linkSocial.telegram}
                    target="_blank"
                    className="text-neutral-alpha-800 hover:text-neutral-alpha-1000 "
                  >
                    <Telegram />
                  </a>
                </div>
              </div>
            </div>
          ) : (
            <div
              className={`border-white-50 bg-white-50 h-full border border-r`}
            >
              <CollapseContent
                setIsHideLeftPanelState={setIsHideLeftPanelState}
                isHideLeftPanelState={isHideLeftPanelState}
              />
            </div>
          )}
        </div>
      </div>
      {isShowBoostModal && (
        <ModalBoost
          isOpen={isShowBoostModal}
          onClose={() => setIsShowBoostModal(false)}
          pair={pair}
        />
      )}
    </>
  );
};
