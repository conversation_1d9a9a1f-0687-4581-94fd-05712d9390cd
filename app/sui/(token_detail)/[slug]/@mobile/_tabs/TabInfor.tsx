import React from "react";
import { PairAuditCheck } from "@/components/Pair/AuditCheck";
import CurveProcessing from "@/components/Pair/CurveProcessing";
import { TokenInfos } from "@/components/Pair/TokenInfos";
import { PairTradingStats } from "@/components/Pair/PairTradingStats";
import { AppButton } from "@/components";
import { FlashIconBoost } from "@/assets/icons";
import ModalBoost from "@/modals/ModalBoost";
import { TPair } from "@/types";
import moment from "moment";
import { getTimeFormatBoots } from "@/utils/helper";
import { PoolRelated } from "@/components/Pair/PoolRelated";
import { usePairBoost } from "@/app/sui/(token_detail)/_hooks/usePairBoost";

export const TabInfor = ({ pair }: { pair: TPair }) => {
  const { isShowBoostModal, setIsShowBoostModal } = usePairBoost();

  return (
    <div className="tablet:p-2 flex flex-col items-center justify-center p-0">
      <div className="tablet:border-r border-neutral-alpha-50 tablet:bg-neutral-alpha-50 relative h-full w-full max-w-[640px]">
        <div className={`part-info customer-scroll h-full overflow-auto`}>
          <TokenInfos pair={pair} />
          <PairTradingStats pair={pair} />
          <div className="gap-2 p-2">
            <AppButton
              size="large"
              variant="boost"
              onClick={() => setIsShowBoostModal(true)}
            >
              <div className="flex items-center gap-1">
                <FlashIconBoost />
                <span>Boost</span>
                {pair?.tokenBase?.boostFactor &&
                  moment(
                    pair?.tokenBase?.isBoostedUntil,
                    "YYYY-MM-DD H:mm:ss.S Z"
                  ).valueOf() > moment().valueOf() && (
                    <span className="body-xs-medium-10 inline-flex h-[20px] items-center justify-center rounded border border-orange-900 bg-orange-900 px-1">
                      {getTimeFormatBoots(pair?.tokenBase?.boostFactor)}
                    </span>
                  )}
              </div>
            </AppButton>
          </div>
          <PairAuditCheck />
        </div>
      </div>
      <div className="tablet:border-l border-neutral-alpha-50 relative h-full w-full max-w-[640px] px-2.5">
        <div className="customer-scroll h-full overflow-auto">
          <CurveProcessing />
          <PoolRelated pair={pair} />
        </div>
      </div>

      <ModalBoost
        isOpen={isShowBoostModal}
        onClose={() => setIsShowBoostModal(false)}
        pair={pair}
      />
    </div>
  );
};
