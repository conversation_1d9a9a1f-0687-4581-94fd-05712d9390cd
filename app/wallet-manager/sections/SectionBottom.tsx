import {
  CherronUpIcon,
  ChevronDownIcon,
  EyeHideIcon,
  IMyTradeIcon,
  IPositionIcon,
  MinusIcon,
} from "@/assets/icons";
import {
  TableMyClosedPosition,
  TableMyPosition,
  TableMyTrade,
} from "@/components";
import { TableMyHiddenPositions } from "@/components/Table/TableMyHiddenPositions";
import { RootState } from "@/store";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";

export const SectionBottom = ({
  sectionExpanded,
  expand,
  hide,
  heightContent,
}: {
  heightContent: number;
  sectionExpanded: string | null;
  expand?: (value: string) => void;
  hide?: (value: string) => void;
}) => {
  const [activeTab, setActiveTab] = useState<string>("my-positions");

  const dataTableRef = useRef<HTMLDivElement>(null);
  const positions = useSelector((state: RootState) => state.user.positions);

  const TABS = [
    {
      id: "my-positions",
      name: "Open Positions",
      icon: IPositionIcon,
    },
    {
      id: "my-closed-positions",
      name: "Closed Positions",
      icon: IPositionIcon,
    },
    {
      id: "my-trades",
      name: "My Trades",
      icon: IMyTradeIcon,
    },
    {
      id: "hidden-positions",
      name: "Hidden",
      icon: EyeHideIcon,
    },
  ].filter(Boolean);

  useEffect(() => {
    (dataTableRef.current as any)?.filter({});
  }, []);

  const renderActiveTabContent = () => {
    switch (activeTab) {
      case "my-positions": {
        return <TableMyPosition heightContent={heightContent} />;
      }
      case "my-closed-positions": {
        return <TableMyClosedPosition heightContent={heightContent} />;
      }
      case "my-trades": {
        return <TableMyTrade heightContent={heightContent - 46} />;
      }
      case "hidden-positions": {
        return <TableMyHiddenPositions heightContent={heightContent} />;
      }
      default: {
        return <TableMyPosition heightContent={heightContent} />;
      }
    }
  };

  return (
    <>
      <div className="border-neutral-alpha-50 flex h-[40px] items-center justify-between border-b px-[16px]">
        <div className="text-neutral-alpha-1000 action-sm-semibold-12 hide-scroll flex items-center gap-2 overflow-x-scroll">
          <div className="flex">
            {TABS.map((item: any, index) => {
              return (
                <div
                  onClick={() => setActiveTab(item.id)}
                  key={index}
                  className={`hover:text-neutral-alpha-1000 active-tab flex w-max cursor-pointer items-center gap-1 px-4 py-3 text-[12px] leading-[16px] hover:font-semibold ${
                    activeTab === item.id
                      ? "text-neutral-alpha-1000 border-neutral-alpha-500 border-b font-semibold"
                      : "text-neutral-alpha-800 border-0 font-normal"
                  }`}
                >
                  <item.icon
                    className={`h-[16px] w-[16px] ${
                      activeTab === item.id
                        ? "custom-opacity-svg-active text-white"
                        : "custom-opacity-svg-inactive"
                    }`}
                  />
                  {item.name}{" "}
                  {item.id === "my-positions" && positions?.length > 0
                    ? `(${positions?.length})`
                    : ""}
                </div>
              );
            })}
          </div>
        </div>

        <div className="tablet:flex hidden">
          <div className="flex cursor-pointer items-center p-2">
            {sectionExpanded === "WALLET" ? (
              <MinusIcon className="cursor-not-allowed opacity-50" />
            ) : (
              <MinusIcon onClick={() => hide && hide("TABLE")} />
            )}
          </div>
          <div className="cursor-pointer p-2">
            {sectionExpanded === "TABLE" ? (
              <ChevronDownIcon
                width={16}
                onClick={() => hide && hide("TABLE")}
              />
            ) : (
              <CherronUpIcon onClick={() => expand && expand("TABLE")} />
            )}
          </div>
        </div>
      </div>

      {renderActiveTabContent()}
    </>
  );
};
