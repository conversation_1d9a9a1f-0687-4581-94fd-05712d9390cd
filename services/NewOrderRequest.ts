import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class NewOrderRequest extends BaseRequest {
  getUrlPrefix() {
    return config.newOrderUrl;
  }

  async createOrderQuickBuy(network: string, params: any) {
    const url = `/${network}/orders/quick-buy`;
    return this.post(url, params);
  }

  async createOrderQuickSell(network: string, params: any) {
    const url = `/${network}/orders/quick-sell`;
    return this.post(url, params);
  }

  async createOrderSellLimit(network: string, params: any) {
    const url = `/${network}/orders/sell-limit`;
    return this.post(url, params);
  }

  async createOrderSellDCA(network: string, params: any) {
    const url = `/${network}/orders/sell-dca`;
    return this.post(url, params);
  }

  async createOrderBuyLimit(network: string, params: any) {
    const url = `/${network}/orders/buy-limit`;
    return this.post(url, params);
  }

  async createOrderBuyDCA(network: string, params: any) {
    const url = `/${network}/orders/buy-dca`;
    return this.post(url, params);
  }

  async closePosition(network: string, params: any) {
    const url = `/${network}/orders/close-position`;
    return this.post(url, params);
  }

  async claimMyLockedAmount(network: string, params: any) {
    const url = `/${network}/orders/unlock-coin`;
    return this.post(url, params);
  }

  async getOrders(network: string, params: any) {
    const url = `/${network}/orders`;
    return this.get(url, params);
  }

  async getOpenOrders(network: string, params: any) {
    const url = `/${network}/open-limit-orders`;
    return this.get(url, params);
  }

  async cancelOrder(network: string, orderId: string) {
    const url = `/${network}/orders/${orderId}/revoke`;
    return this.put(url);
  }

  async cancelAllOrder(network: string) {
    const url = `/${network}/orders/revoke-all`;
    return this.put(url);
  }

  async claimMoonBags(network: string, params: any) {
    const url = `/${network}/orders/moonbags/claim`;
    return this.post(url, params);
  }

  async stakingMoonBags(network: string, params: any) {
    const url = `/${network}/orders/moonbags/staking`;
    return this.post(url, params);
  }

  async unstakingMoonBags(network: string, params: any) {
    const url = `/${network}/orders/moonbags/unstaking`;
    return this.post(url, params);
  }
}
