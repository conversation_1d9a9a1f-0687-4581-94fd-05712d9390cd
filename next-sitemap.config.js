/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: "https://raidenx.io",
  changefreq: "daily",
  generateRobotsTxt: true,
  generateIndexSitemap: true,
  exclude: [
    "/sitemap/newpair.xml",
    "/sitemap/trending1h.xml",
    "/sitemap/trending5m.xml",
    "/sitemap/trending6h.xml",
    "/sitemap/trending24h.xml",
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [],
        crawlDelay: 1800,
      },
    ],
    additionalSitemaps: [
      "https://raidenx.io/sitemap/newpair.xml",
      "https://raidenx.io/sitemap/trending1h.xml",
      "https://raidenx.io/sitemap/trending5m.xml",
      "https://raidenx.io/sitemap/trending6h.xml",
      "https://raidenx.io/sitemap/trending24h.xml",
    ],
  },
};
