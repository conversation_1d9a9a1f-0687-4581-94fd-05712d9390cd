import { useCallback } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { TPair } from "@/types";
import DexesFactory from "../utils/simulates/dexes/DexesFactory";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import { getReferenceGasPrice, getOwnerCoinOnchain } from "../utils/suiClient";
import { convertDecToMist } from "../utils/helper";
import { SUI_DECIMALS, SUI_TOKEN_ADDRESS_SHORT } from "../utils/contants";
import useProcessTx from "./useProcessTx";
import { toastError } from "../libs/toast";
import { EDex } from "../enums/dex.enum";
import BigNumber from "bignumber.js";
import config from "@/config";

export const useExternalWallet = () => {
  const currentAccount = useCurrentAccount();
  const { processTx } = useProcessTx();

  const onBuyToken = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair?.dex.dex as any,
          objectId: pair?.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        const exactAmountIn = convertDecToMist(buyAmount, SUI_DECIMALS);
        let output: { tx: Transaction; amountOut: string | number } | null =
          null;
        const raidenxFee = BigNumber(exactAmountIn)
          .times(config.raidenxFeeRate)
          .toFixed();
        const amountDeductedForRaidenxFee = BigNumber(exactAmountIn)
          .minus(raidenxFee)
          .toFixed();

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          output = await instance.buildBuyTransaction(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenBase,
            pair?.tokenQuote,
            pool?.objectId,
            gasBasePrice
          );
        } else if (pool.dex === EDex.SUIAIFUN) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice,
            true // isBuyBySuiToken
          );
        } else if (pool.dex === EDex.STEAM) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            exactAmountIn,
            pair?.tokenQuote,
            pair?.tokenBase,
            pair.isXQuoteToken,
            pair.feeTier,
            pool.objectId,
            gasBasePrice
          );
        } else {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice
          );
        }

        if (!output?.tx) {
          throw new Error("Failed to build transaction");
        }

        const raidenxFeeCoin = coinWithBalance({
          balance: BigInt(raidenxFee),
          type: SUI_TOKEN_ADDRESS_SHORT,
          useGasCoin: true,
        });
        output.tx.transferObjects(
          [raidenxFeeCoin],
          output.tx.pure.address(config.raidenxAddress)
        );

        await processTx(output.tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const onSellToken = useCallback(
    async (
      pair: TPair,
      sellPercent: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();

        // Calculate adjusted sell percentage that accounts for RaidenX fee
        // If user wants to sell 50%, we sell 49.5% and transfer 0.5% as fee
        const feeRate = config.raidenxFeeRate; // 0.01 = 1%
        const sellPercentFloat = parseFloat(sellPercent);
        const actualSellPercent = sellPercentFloat * (1 - feeRate);
        const feePercent = sellPercentFloat * feeRate;

        console.log(
          `Original: ${sellPercent}%, Actual sell: ${actualSellPercent.toFixed(
            6
          )}%, Fee: ${feePercent.toFixed(6)}%`
        );

        // Get the sell transaction for the adjusted amount (avoids duplicate getOwnerCoinOnchain)
        const tx = await instance.extractQuoteTokenOut(
          currentAccount.address,
          pair?.tokenBase,
          pair?.tokenQuote,
          actualSellPercent.toString(),
          pool.objectId,
          gasBasePrice
        );

        // Add RaidenX fee transfer using coinWithBalance
        if (feePercent > 0) {
          // Get current balance to calculate exact fee amount
          const [, currentTokenBalance] = await getOwnerCoinOnchain(
            currentAccount.address,
            pair?.tokenBase?.address
          );

          const feeAmount = currentTokenBalance
            .multipliedBy(feePercent)
            .div(100)
            .integerValue(BigNumber.ROUND_FLOOR);

          console.log(`Fee amount: ${feeAmount.toFixed()} tokens`);

          if (feeAmount.isGreaterThan(0)) {
            const raidenxFeeCoin = coinWithBalance({
              balance: BigInt(feeAmount.toFixed()),
              type: pair?.tokenBase?.address,
              useGasCoin: false,
            });
            tx.transferObjects(
              [raidenxFeeCoin],
              tx.pure.address(config.raidenxAddress)
            );
          }
        }

        await processTx(tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return {
    onBuyToken,
    onSellToken,
  };
};
