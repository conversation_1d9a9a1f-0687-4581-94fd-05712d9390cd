import { useCallback } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { TPair } from "@/types";
import DexesFactory from "../utils/simulates/dexes/DexesFactory";
import { coinWithBalance, Transaction } from "@mysten/sui/transactions";
import { getReferenceGasPrice } from "../utils/suiClient";
import { convertDecToMist } from "../utils/helper";
import { SUI_DECIMALS, SUI_TOKEN_ADDRESS_SHORT } from "../utils/contants";
import useProcessTx from "./useProcessTx";
import { toastError } from "../libs/toast";
import { EDex } from "../enums/dex.enum";
import BigNumber from "bignumber.js";
import config from "@/config";
import { getOwnerCoinOnchain } from "../utils/suiClient";

export const useExternalWallet = () => {
  const currentAccount = useCurrentAccount();
  const { processTx } = useProcessTx();

  const onBuyToken = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair?.dex.dex as any,
          objectId: pair?.poolId,
        };

        setIsLoading(true);
        let tx: Transaction;
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        const exactAmountIn = convertDecToMist(buyAmount, SUI_DECIMALS);
        const raidenxFee = BigNumber(exactAmountIn)
          .times(config.raidenxFeeRate)
          .toFixed(0);
        const amountDeductedForRaidenxFee = BigNumber(exactAmountIn)
          .minus(raidenxFee)
          .toFixed();

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          tx = await instance.buildBuyTransaction(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenBase,
            pair?.tokenQuote,
            pool?.objectId,
            gasBasePrice
          );
        } else if (pool.dex === EDex.SUIAIFUN) {
          tx = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice,
            true // isBuyBySuiToken
          );
        } else {
          tx = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice
          );
        }

        const raidenxFeeCoin = coinWithBalance({
          balance: BigInt(raidenxFee),
          type: SUI_TOKEN_ADDRESS_SHORT,
          useGasCoin: true,
        });
        console.log(raidenxFeeCoin, "raidenxFeeCoin");
        tx.transferObjects(
          [raidenxFeeCoin],
          tx.pure.address(config.raidenxAddress)
        );

        await processTx(tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const onSellToken = useCallback(
    async (
      pair: TPair,
      sellPercent: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();

        const [, currentTokenBalance] = await getOwnerCoinOnchain(
          currentAccount.address,
          pair?.tokenBase?.address
        );

        if (currentTokenBalance.isZero()) {
          throw new Error("Insufficient token balance");
        }
        console.log(currentTokenBalance.toFixed(), "currentTokenBalance");

        const intendedSellTokenAmount = currentTokenBalance
          .multipliedBy(parseFloat(sellPercent))
          .div(100);

        const raidenxFee = intendedSellTokenAmount
          .times(config.raidenxFeeRate)
          .integerValue(BigNumber.ROUND_FLOOR);

        const actualSellTokenAmount = intendedSellTokenAmount.minus(raidenxFee);

        const adjustedSellPercent = actualSellTokenAmount
          .div(currentTokenBalance)
          .times(100)
          .toFixed(0);

        const tx = await instance.extractQuoteTokenOut(
          currentAccount.address,
          pair?.tokenBase,
          pair?.tokenQuote,
          adjustedSellPercent,
          pool.objectId,
          gasBasePrice
        );

        // const raidenxFeeCoin = coinWithBalance({
        //   balance: BigInt(raidenxFee.toFixed()),
        //   type: pair?.tokenBase?.address,
        //   useGasCoin: false,
        // });
        // tx.transferObjects(
        //   [raidenxFeeCoin],
        //   tx.pure.address(config.raidenxAddress)
        // );

        await processTx(tx, onSuccess);
      } catch (error: any) {
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return {
    onBuyToken,
    onSellToken,
  };
};
