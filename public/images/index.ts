import AUX from "./_aux.png";
import AnimeSwap from "./animeSwap.png";
import BannerAirdropCampaign from "./BannerAirdropCampaign.png";
import FlexBgUp from "./Flex-ver-1-min.png";
import FlexBgDown from "./Flex-ver-2-min.png";
import Logo from "./logo.png";
import QrIcon from "./qrIcon.png";
import ICheckboxIcon from "./icons/CheckboxIcon.png";
import ITrendingIcon from "./icons/TrendingIcon.png";
import ICheckedIcon from "./icons/CheckedIcon.png";
import FlexBgUpResponsive from "./Flex-ver-3-min.png";
import FlexBgDownResponsive from "./Flex-ver-4-min.png";
import BgMyReferral from "./bg_my_referral.png";
import BgMyReferralMobile from "./bg_my_referral_mobile.png";
import BgMyReferralFooter from "./bg_my_referral_1.png";
import DividerMyReferral from "./divider_my_referral.png";
import DividerMyReferralMobile from "./divider_my_referral_mobile.png";
import TermConditionsReferral from "./TermConditionsReferral.png";
import DefaultToken from "./DefaultToken.png";
import BgGetStart from "./bg-get-start.png";
import BgAddWallet from "./bg-add-wallet.png";
import BgConnectTelegram from "./bg-connect-telegram.png";
import BgInstallApp from "./bg-install-app.png";
import BgToken from "./BgToken.png";
import Bluemove from "./bluemove.png";
import ButtonBanner from "./button_banner.png";
import ButtonBannerMobile from "./button_banner_mobile.png";
import BVM from "./BVM.png";
import Cellana from "./cellana.png";
import Cetus from "./cetus.png";
import Flowx from "./flowx.png";
import BgInstall from "./home-page/bg-install-app.png";
import DEXVolumeAlert from "./home-page/DEX-volume-alert.png";
import GraduatedTokenAlert from "./home-page/graduated-token-alert.png";
import ImageAppMobile from "./home-page/image-app-mobile.png";
import ImageMobile from "./home-page/image-screen-mobile.png";
import ImageInstantAccess from "./home-page/instant-access.png";
import ImageMobileFriendly from "./home-page/mobile-friendly.png";
import FunZoneMobile from "./home-page/funzone.png";
import HopFun from "./hop_fun.png";
import ImageToken from "./ImageToken.png";
import Liquidswap from "./liquidswap.png";
import MagicDegend from "./magic-degend.png";
import ClearSpace from "./media-kit/clear-space.png";
import Partnership from "./media-kit/partnership.png";
import MerlinChain from "./merlin-chain.png";
import Movepump from "./movepump.png";
import OKX from "./OKX.png";
import Pancake from "./pancake.png";
import Pumpfun from "./pumpfun.png";
import SevenKFun from "./sevenkfun.png";
import Thala from "./thala.png";
import TurbosFinance from "./turbos_finance.png";
import Uptos from "./uptos.png";
import Bluefin from "./bluefin-logo.png";
import Suiai from "./suiai-logo.png";
import TelegramBotImage from "./home-page/telegram-bot.png";
import BuyBotImage from "./home-page/buy-bot.png";
import Aggregator from "./home-page/Aggregator.png";
import CETUS from "./home-page/Cetus.png";
import Fun from "./home-page/Fun.png";
import Shio from "./home-page/Shio.png";
import Sui from "./home-page/Sui.png";
import Suipiens from "./home-page/Suipiens.png";
import SuiVision from "./home-page/SuiVision.png";
import Turbos from "./home-page/Turbos.png";
import FlowXFinance from "./home-page/FlowXFinance.png";
import MonitorImage from "./home-page/monitor.png";
import ImageMobileApp from "./home-page/ImageMobileApp.png";
import TabBar from "./home-page/TabBar.png";
import Ecosystem from "./home-page/ecosystem.png";
import MoonbagProject from "./home-page/moonbag-project.png";
import RaidenXProject from "./home-page/raidenx-project.png";
import EcosystemMobile from "./home-page/ecosystem-mobile.png";
import MoonBags from "./moonbags.png";
import BgCardBoost from "./bg-card-boost.png";
import KeyImage from "./keyImage.png";

export {
  AUX,
  AnimeSwap,
  BVM,
  BannerAirdropCampaign,
  Suiai,
  ImageToken,
  BgToken,
  Liquidswap,
  Bluemove,
  Cetus,
  Movepump,
  Pumpfun,
  Pancake,
  Thala,
  Cellana,
  Flowx,
  TurbosFinance,
  Uptos,
  FlexBgUp,
  FlexBgDown,
  FlexBgUpResponsive,
  FlexBgDownResponsive,
  Logo,
  QrIcon,
  ICheckboxIcon,
  ITrendingIcon,
  ICheckedIcon,
  BgMyReferral,
  BgMyReferralMobile,
  DividerMyReferral,
  DividerMyReferralMobile,
  BgMyReferralFooter,
  TermConditionsReferral,
  DefaultToken,
  BgGetStart,
  BgAddWallet,
  BgConnectTelegram,
  BgInstall,
  BgInstallApp,
  ButtonBanner,
  ButtonBannerMobile,
  ClearSpace,
  DEXVolumeAlert,
  GraduatedTokenAlert,
  HopFun,
  ImageAppMobile,
  ImageInstantAccess,
  ImageMobile,
  ImageMobileFriendly,
  MagicDegend,
  MerlinChain,
  OKX,
  Partnership,
  SevenKFun,
  Bluefin,
  FunZoneMobile,
  TelegramBotImage,
  BuyBotImage,
  Aggregator,
  CETUS,
  FlowXFinance,
  Fun,
  Shio,
  Sui,
  SuiVision,
  Suipiens,
  Turbos,
  MonitorImage,
  ImageMobileApp,
  TabBar,
  Ecosystem,
  RaidenXProject,
  MoonbagProject,
  EcosystemMobile,
  MoonBags,
  BgCardBoost,
  KeyImage,
};
