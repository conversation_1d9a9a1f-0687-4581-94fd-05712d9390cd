import { TradingType } from "@/enums/pair.enum";

export type TPairToken = TCoinMetadata & {
  trendingRank?: number;
  createdAt: string;
  name: string;
  priceUsd: string;
  price: string;
  symbol: string;
  totalSupply: string;
  circulatingSupply?: string;
  updatedAt: string;
  volumeUsd: string;
  bannerImageUrl?: string;
  logoImageUrl?: string;
  lockAmount: string;
  lockedAmount: string;
  lockTimestamp: number;
  socials: InfoPair;
  deployerBalancePercent: number;
  freezeAuthority: boolean;
  deployer: string;
  mintAuthority: boolean;
  top10HolderPercent: number;
  amountBurned: number;
  holdersCount: number;
  boostFactor: number;
  isBoostedUntil: number;
  isDexscreenerVerified?: boolean;
};

export type TDex = {
  dex: string;
  name: string;
  network: string;
  version: string;
};

export type TWebsite = {
  label: string;
  url: string;
};

export type TSocial = {
  type: string;
  url: string;
};

export type TDexscreener = {
  type: string;
  url: string;
};

export type InfoPair = {
  imageUrl: string;
  websites: TWebsite[];
  socials: TSocial[];
  dexscreener: TDexscreener[];
};

export type TPairPrice = {
  price: string;
  priceUsd: string;
};

export type TPair = {
  createdAt: string;
  liquidity: string;
  bondingCurve: number;
  liquidityUsd: string;
  pairId: string;
  reserveBase: string;
  reserveQuote: string;
  tokenBase: TPairToken;
  tokenQuote: TPairToken;
  totalMakers: number;
  totalTxns: number;
  buyTxns: number;
  sellTxns: number;
  updateAt: string;
  volume: string;
  volumeUsd: string;
  network: string;
  slug: string;
  deployer: string;
  timestamp: number;
  dex: TDex;
  totalHolders: number;
  stats: TPairStats;
  marketCapUsd?: string;
  priceUsd?: string;
  priceSui?: string;
  graduatedSlug?: string;
  poolId: string;
  alias?: string;
  lpBurned: string | number | null;
  lpSupply: string | number | null;
  trendingRank?: string | number;
  recentDevActionStatus?: TDevActionStatus;
};

export type TDevActionStatus = {
  recentDevAction: string;
  percent: number;
};

export type TTrader = {
  baseAmountBought: string;
  baseAmountSold: string;
  boughtTxs: string;
  maker: string;
  quoteAmountBought: string;
  quoteAmountSold: string;
  soldTxs: string;
  tradedVolumeUsd: string;
  volumeUsdBought: string;
  volumeUsdSold: string;
  alias?: string;
};

export type THolder = {
  ownedPercent: string;
  balance: string;
  balanceUsd: string;
  createdAt: string;
  updatedAt: string;
  walletAddress: string;
  walletType: string;
  token: {
    address: string;
    logoImageUrl: string;
    name: string;
    symbol: string;
  };
  alias?: string;
};

export type TAudit = {
  createdAt: string;
  lpBurned: number;
  lpBurnedPercent: string;
  updatedAt: string;
};

export type TPairStat = {
  [key: string]: number;
};

export type TPairStats = {
  buyTxn: TPairStat;
  buyVolume: TPairStat;
  buyer: TPairStat;
  maker: TPairStat;
  pairId: string;
  percent: TPairStat;
  sellTxn: TPairStat;
  sellVolume: TPairStat;
  seller: TPairStat;
  totalNumTxn: TPairStat;
  volume: TPairStat;
};
export type TTokenSocket = {
  network: string;
  address: string;
  name: string;
  symbol: string;
  decimals: number;
  totalSupply: string;
  circulatingSupply: string;
  price: string;
  priceUsd: string;
  volumeUsd: string;
  isRug: boolean;
  bannerImageUrl: string;
  logoImageUrl: string;
  socials: TSocial | TSocial[];
  lockAmount: string;
  lockedAmount: string;
  lockTimestamp: number;
  createdAt: string;
  updatedAt: string;
};
export type TPairSocket = {
  network: string;
  dex: TDex;
  pairId: string;
  poolId: string;
  deployer: string;
  slug: string;
  tokenBase: TTokenSocket;
  tokenQuote: TTokenSocket;
  reserveBase: string;
  reserveQuote: string;
  createdAt: string;
  updatedAt: string;
};

export type TPrivateSubscribed = {
  status: string;
  rooms: string[];
};

export type TPairsStatsSocket = {
  pairId: string;
  percent: TPairStat;
  volume: TPairStat;
  marketCapPercent: TPairStat;
  maker: TPairStat;
  buyer: TPairStat;
  seller: TPairStat;
  buyVolume: TPairStat;
  sellVolume: TPairStat;
  buyTxn: TPairStat;
  sellTxn: TPairStat;
  totalNumTxn: TPairStat;
  volumeUsdPercent: TPairStat;
  liquidityUsdPercent: TPairStat;
  txPercent: TPairStat;
  uniqueMaker: TPairStat;
};
export type TTokenInfoSocialSocket = {
  address: string;
  websites: TWebsite[];
  socials: TSocial[];
  bannerImageUrl?: string;
  logoImageUrl?: string;
};

export type TTokenInfoAuditSocket = {
  address: string;
  mintAuthority: boolean;
  freezeAuthority: boolean;
  coinDev: string;
  amountBurned: number;
  suspiciousActivities: {
    level: string;
    text: string;
  }[];
  lpBurned: number;
  top10HolderPercent: number;
  devBalancePercent: number;
  lpBurnedPercent: string;
  updatedAt: string;
  createdAt: string;
};
export type TPairTransactionSocket = {
  network: string;
  pairId: string;
  hash: string;
  index: number;
  tradingType: TradingType;
  maker: {
    address: string;
    traderCategory: string;
    walletType: string;
  };
  baseAmount: string;
  quoteAmount: string;
  price: string;
  priceUsd: string;
  totalUsd: string;
  version: number;
  timestamp: number;
};

export type TPositionUpdatedSocket = {
  pairId: string;
  baseAmountBought: string;
  baseAmountSold: string;
  quoteAmountBought: string;
  quoteAmountSold: string;
  volumeUsdBought: string;
  volumeUsdSold: string;
  balance: string;
  balanceUsd: string;
  walletAddress: string;
  token: {
    address: string;
    name: string;
    symbol: string;
    decimals: number;
  };
};
export type TTradeSucceededSocket = {
  network: string;
  pairId: string;
  hash: string;
  index: number;
  tradingType: TradingType;
  makerAddress: string;
  baseAmount: string;
  quoteAmount: string;
  price: string;
  priceUsd: string;
  totalUsd: string;
  version: number;
  timestamp: number;
};

export type TPairTransaction = {
  isNew?: boolean;
  baseAmount: string;
  blockNumber: number;
  hash: string;
  index?: number;
  token?: string;
  maker: {
    address: string;
    traderCategory: string;
  };
  pairId: string;
  price: string;
  priceUsd: string;
  quoteAmount: string;
  timestamp: number;
  totalUsd: string;
  tradingType: TradingType;
  version: number;
};

export type TTokenProfile = {
  network: string;
  type: string;
  tokenAddress: string;
  contact: string;
  twitter: string;
  website: string;
  telegram: string;
  logoImage: File | null;
  bannerImage: File | null;
  takeoverExplanation?: string;
  payerAddress: string;
  paymentType: string;
};
export type TPairStatsWsUpdated = {
  pairId: string;
  network: string;
  percent: TPairStat;
  volume: TPairStat;
  marketCapPercent: TPairStat;
  maker: TPairStat;
  buyer: TPairStat;
  seller: TPairStat;
  buyVolume: TPairStat;
  sellVolume: TPairStat;
  buyTxn: TPairStat;
  sellTxn: TPairStat;
  totalNumTxn: TPairStat;
  volumeUsdPercent: TPairStat;
  liquidityUsdPercent: TPairStat;
  txPercent: TPairStat;
  uniqueMaker: TPairStat;
  bondingCurve: string;
  buyTxns: number;
  sellTxns: number;
  liquidity: string;
  liquidityUsd: string;
  marketCapUsd: string;
  priceSui: string;
  priceUsd: string;
  reserveBase: string;
  reserveQuote: string;
  totalTxns: number;
  volumeUsd: number;
};

export type TCoinMetadata = {
  address: string;
  decimals: number;
  symbol?: string;
  name?: string;
  description?: string;
  iconUrl?: string;
  id?: string;
};
